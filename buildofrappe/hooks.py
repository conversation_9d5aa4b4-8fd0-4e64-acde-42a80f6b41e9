app_name = "buildofrappe"
app_title = "Buildo Frappe"
app_publisher = "<PERSON><PERSON>"
app_description = "Construction Management App for Builders and Contractors"
app_email = "<EMAIL>"
app_license = "mit"

# Apps
# ------------------

# required_apps = []

# Each item in the list will be shown as an app in the apps page
# add_to_apps_screen = [
# 	{
# 		"name": "buildofrappe",
# 		"logo": "/assets/buildofrappe/logo.png",
# 		"title": "Buildo Frappe",
# 		"route": "/buildofrappe",
# 		"has_permission": "buildofrappe.api.permission.has_app_permission"
# 	}
# ]

# Includes in <head>
# ------------------

# include js, css files in header of desk.html
# app_include_css = "/assets/buildofrappe/css/buildofrappe.css"
# app_include_js = "/assets/buildofrappe/js/buildofrappe.js"

# include js, css files in header of web template
# web_include_css = "/assets/buildofrappe/css/buildofrappe.css"
# web_include_js = "/assets/buildofrappe/js/buildofrappe.js"

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "buildofrappe/public/scss/website"

# include js, css files in header of web form
# webform_include_js = {"doctype": "public/js/doctype.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
# page_js = {"page" : "public/js/file.js"}

# include js in doctype views
doctype_js = {
	"Client Invoice": "buildo_frappe/doctype/client_invoice/client_invoice.js",
	"Payment Received": "buildo_frappe/doctype/payment_received/payment_received.js",
	"Payment Allocation": "buildo_frappe/doctype/payment_allocation/payment_allocation.js"
}
# doctype_list_js = {"doctype" : "public/js/doctype_list.js"}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Svg Icons
# ------------------
# include app icons in desk
# app_include_icons = "buildofrappe/public/icons.svg"

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
# 	"Role": "home_page"
# }

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Jinja
# ----------

# add methods and filters to jinja environment
# jinja = {
# 	"methods": "buildofrappe.utils.jinja_methods",
# 	"filters": "buildofrappe.utils.jinja_filters"
# }

# Installation
# ------------

# before_install = "buildofrappe.install.before_install"
# after_install = "buildofrappe.install.after_install"

# Uninstallation
# ------------

# before_uninstall = "buildofrappe.uninstall.before_uninstall"
# after_uninstall = "buildofrappe.uninstall.after_uninstall"

# Integration Setup
# ------------------
# To set up dependencies/integrations with other apps
# Name of the app being installed is passed as an argument

# before_app_install = "buildofrappe.utils.before_app_install"
# after_app_install = "buildofrappe.utils.after_app_install"

# Integration Cleanup
# -------------------
# To clean up dependencies/integrations with other apps
# Name of the app being uninstalled is passed as an argument

# before_app_uninstall = "buildofrappe.utils.before_app_uninstall"
# after_app_uninstall = "buildofrappe.utils.after_app_uninstall"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "buildofrappe.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
# 	"Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
# 	"Event": "frappe.desk.doctype.event.event.has_permission",
# }

# Realtime Events
# ---------------
# Enable realtime events for invoice balance updates

realtime_events = [
	"invoice_balance_updated",
	"payment_allocation_updated"
]

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
# 	"ToDo": "custom_app.overrides.CustomToDo"
# }

# Document Events
# ---------------
# Hook on document methods and events

doc_events = {
	"Payment Allocation": {
		"after_insert": "buildofrappe.buildo_frappe.utils.payment_allocation.on_payment_allocation_change",
		"on_update": "buildofrappe.buildo_frappe.utils.payment_allocation.on_payment_allocation_change",
		"before_delete": "buildofrappe.buildo_frappe.utils.payment_allocation.on_payment_allocation_delete"
	}
}

# Scheduled Tasks
# ---------------

# scheduler_events = {
# 	"all": [
# 		"buildofrappe.tasks.all"
# 	],
# 	"daily": [
# 		"buildofrappe.tasks.daily"
# 	],
# 	"hourly": [
# 		"buildofrappe.tasks.hourly"
# 	],
# 	"weekly": [
# 		"buildofrappe.tasks.weekly"
# 	],
# 	"monthly": [
# 		"buildofrappe.tasks.monthly"
# 	],
# }

# Testing
# -------

# before_tests = "buildofrappe.install.before_tests"

# Overriding Methods
# ------------------------------
#
# override_whitelisted_methods = {
# 	"frappe.desk.doctype.event.event.get_events": "buildofrappe.event.get_events"
# }

# Whitelisted Methods
# -------------------
# Methods that can be called from client-side

whitelisted_methods = [
	"buildofrappe.buildo_frappe.doctype.client_invoice.client_invoice.get_invoices_for_customer_project",
	"buildofrappe.buildo_frappe.doctype.client_invoice.client_invoice.get_invoice_items",
	"buildofrappe.buildo_frappe.doctype.client_invoice.client_invoice.get_bank_details",
	"buildofrappe.buildo_frappe.utils.payment_allocation.recalculate_invoice_balance",
	"buildofrappe.buildo_frappe.utils.payment_allocation.get_outstanding_invoices_for_payment",
	"buildofrappe.buildo_frappe.utils.payment_allocation.allocate_payment_manually",
	"buildofrappe.buildo_frappe.doctype.salary.salary.fetch_active_staff"
]
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
# 	"Task": "buildofrappe.task.get_dashboard_data"
# }

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]

# Ignore links to specified DocTypes when deleting documents
# -----------------------------------------------------------

# ignore_links_on_delete = ["Communication", "ToDo"]

# Request Events
# ----------------
# before_request = ["buildofrappe.utils.before_request"]
# after_request = ["buildofrappe.utils.after_request"]

# Job Events
# ----------
# before_job = ["buildofrappe.utils.before_job"]
# after_job = ["buildofrappe.utils.after_job"]

# User Data Protection
# --------------------

# user_data_fields = [
# 	{
# 		"doctype": "{doctype_1}",
# 		"filter_by": "{filter_by}",
# 		"redact_fields": ["{field_1}", "{field_2}"],
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_2}",
# 		"filter_by": "{filter_by}",
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_3}",
# 		"strict": False,
# 	},
# 	{
# 		"doctype": "{doctype_4}"
# 	}
# ]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
# 	"buildofrappe.auth.validate"
# ]

# Automatically update python controller files with type annotations for this app.
# export_python_type_annotations = True

# default_log_clearing_doctypes = {
# 	"Logging DocType Name": 30  # days to retain logs
# }

